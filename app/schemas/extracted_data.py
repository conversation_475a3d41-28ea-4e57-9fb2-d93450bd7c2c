from datetime import UTC, date, datetime
import json
from typing import Self
from uuid import UUID

from pydantic import ConfigDict, Field, field_validator

from constants.extracted_data import DataSourceType
from core.schemas import CustomModel


class ExtractedData(CustomModel):
    conversation_id: UUID = Field(alias='ConversationPublicId')
    data_source_type: DataSourceType = Field(alias='DataSourceType')
    created_at: datetime = Field(alias='CreatedAt')

    activity_name: str | None = Field(default=None, alias='ActivityName')
    client_name: list[str] = Field(default=[], alias='ClientName')
    ldmf_country: list[str] = Field(default=[], alias='LDMFCountry')
    title: str | None = Field(default=None, alias='Title')
    start_date: date | None = Field(default=None, alias='StartDate')
    end_date: date | None = Field(default=None, alias='EndDate')
    industries: str | None = Field(default=None, alias='Industries')
    services: str | None = Field(default=None, alias='Services')
    roles: str | None = Field(default=None, alias='Roles')

    model_config = ConfigDict(
        from_attributes=True,
    )

    @field_validator('client_name', mode='before')
    @classmethod
    def validate_client_name(cls, value: str | None) -> list[str]:
        return [] if value is None else json.loads(value)

    @field_validator('ldmf_country', mode='before')
    @classmethod
    def validate_ldmf_country(cls, value: str | None) -> list[str]:
        return [] if value is None else json.loads(value)

    @classmethod
    def create(cls, conversation_id: UUID, data_source_type: DataSourceType) -> Self:
        return cls.model_validate(
            {
                'conversation_id': conversation_id,
                'data_source_type': data_source_type,
                'created_at': datetime.now(UTC),
            }
        )

    def model_dump_for_db(self) -> dict:
        result = super().model_dump(by_alias=True)
        for field_name in ('ConversationPublicId', 'CreatedAt'):
            result.pop(field_name, None)
        for field_name in ('ClientName', 'LDMFCountry', 'ClientServices', 'TeamAndRoles'):
            if (value := result.get(field_name)) is not None:
                result[field_name] = json.dumps(value)
        return result

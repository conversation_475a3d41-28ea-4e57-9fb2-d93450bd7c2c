ENVIRONMENT='local'
LOG_LEVEL='INFO'
DEBUG='false'

# CORS
ALLOWED_HOSTS='https://kxnextgendevui.deloitteresources.com:4500'

# DB
DB_HOST='127.0.0.1'
DB_PORT='1433'
DB_USER='sa'
DB_PASSWORD='P@ssw0rd_2024_SQL_Secure!'
DB_NAME='genai_quals'
DB_DRIVER='ODBC+Driver+17+for+SQL+Server'

# Azure AD
AZURE_AD_TENANT_ID='36da45f1-dd2c-4d1f-af13-5abe46b99921'
AZURE_AD_API_AUDIENCE='https://dev.kx.deloitte'

# Azure Blob Storage
AZURE_STORAGE_CONNECTION_STRING='DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;BlobEndpoint=http://127.0.0.1:10000/devstoreaccount1;'
AZURE_STORAGE_CONTAINER_NAME='documents'

# HTTP client settings
HTTP_CLIENT_TIMEOUT='30'
HTTP_CLIENT_FOLLOW_REDIRECTS='true'
HTTP_CLIENT_VERIFY_SSL='true'
HTTP_CLIENT_MAX_CONNECTIONS='100'
HTTP_CLIENT_MAX_KEEPALIVE_CONNECTIONS='20'

# KX Dash API settings
KX_DASH_API_BASE_URL='https://devqualsappsai.deloitteresources.com/api/dash'

# Queue settings
AZURE_QUEUE_CONNECTION_STRING='DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;QueueEndpoint=http://127.0.0.1:10001/devstoreaccount1;'
AZURE_DOCUMENT_QUEUE_NAME='document-processing'

# OpenAI settings
AZURE_OPENAI_ENDPOINT='<INSERT_VALUE>'
AZURE_OPENAI_KEY='<INSERT_VALUE>'
AZURE_OPENAI_DEPLOYMENT='gpt-4o'
AZURE_OPENAI_MODEL='gpt-4o'
AZURE_OPENAI_API_VERSION='2024-08-01-preview'

# Industries API settings
INDUSTRIES_API_BASE_URL='https://devqualsappsai.deloitteresources.com/api/client'

# Services API settings
SERVICES_API_BASE_URL='https://devqualsappsai.deloitteresources.com/api/project'

# Roles API settings
ROLES_API_BASE_URL='https://devqualsappsai.deloitteresources.com/api/team-details'

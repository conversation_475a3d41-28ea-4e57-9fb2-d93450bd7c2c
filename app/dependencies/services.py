from typing import Annotated

from fastapi import Depends

from services import (
    ConversationMessageService,
    ConversationService,
    DocumentService,
    ExtractedDataService,
    IntentClassifierService,
    KXDashService,
    IndustryDataService,
    RoleDataService,
    ServiceDataService,
)

from .repositories import (
    ConversationMessageRepositoryDep,
    ConversationRepositoryDep,
    DocumentBlobRepositoryDep,
    DocumentDbRepositoryDep,
    DocumentQueueRepositoryDep,
    ExtractedDataRepositoryDep,
    KXDashRepositoryDep,
    OpenAIRepositoryDep,
    IndustryRepositoryDep,
    RoleRepositoryDep,
    ServiceRepositoryDep,
)


__all__ = [
    'ExtractedDataServiceDep',
    'KXDashServiceDep',
    'IntentClassifierServiceDep',
    'DocumentServiceDep',
    'ConversationMessageServiceDep',
    'DocumentServiceDep',
    'KXDashServiceDep',
    'ConversationServiceDep',
]


def get_intent_classifier_service(openai_service: OpenAIRepositoryDep) -> IntentClassifierService:
    """Get the intent classifier for dependency injection."""
    return IntentClassifierService(openai_service=openai_service)


IntentClassifierServiceDep = Annotated[IntentClassifierService, Depends(get_intent_classifier_service)]


def get_extracted_data_service(
    extracted_data_repository: ExtractedDataRepositoryDep,
    industry_data_service: 'IndustryDataServiceDep',
    role_data_service: 'RoleDataServiceDep',
    service_data_service: 'ServiceDataServiceDep',
) -> ExtractedDataService:
    return ExtractedDataService(
        extracted_data_repository=extracted_data_repository,
        industry_data_service=industry_data_service,
        role_data_service=role_data_service,
        service_data_service=service_data_service,
    )


ExtractedDataServiceDep = Annotated[ExtractedDataService, Depends(get_extracted_data_service)]


def get_kx_dash_service(
    kx_dash_repository: KXDashRepositoryDep,
    extracted_data_service: ExtractedDataServiceDep,
) -> KXDashService:
    return KXDashService(
        kx_dash_repository=kx_dash_repository,
        extracted_data_service=extracted_data_service,
    )


KXDashServiceDep = Annotated[KXDashService, Depends(get_kx_dash_service)]


def get_document_service(
    document_db_repository: DocumentDbRepositoryDep,
    document_blob_repository: DocumentBlobRepositoryDep,
    document_queue_repository: DocumentQueueRepositoryDep,
) -> DocumentService:
    return DocumentService(
        document_db_repository=document_db_repository,
        document_blob_repository=document_blob_repository,
        document_queue_repository=document_queue_repository,
    )


DocumentServiceDep = Annotated[DocumentService, Depends(get_document_service)]


def get_conversation_message_service(
    conversation_message_repository: ConversationMessageRepositoryDep,
    conversation_repository: ConversationRepositoryDep,
    document_service: DocumentServiceDep,
    kx_dash_service: KXDashServiceDep,
    intent_classifier_service: IntentClassifierServiceDep,
) -> ConversationMessageService:
    return ConversationMessageService(
        conversation_message_repository=conversation_message_repository,
        conversation_repository=conversation_repository,
        document_service=document_service,
        kx_dash_service=kx_dash_service,
        intent_classifier_service=intent_classifier_service,
    )


ConversationMessageServiceDep = Annotated[ConversationMessageService, Depends(get_conversation_message_service)]


def get_conversation_service(
    conversation_repository: ConversationRepositoryDep,
    conversation_message_service: ConversationMessageServiceDep,
    extracted_data_service: ExtractedDataServiceDep,
    document_service: DocumentServiceDep,
) -> ConversationService:
    return ConversationService(
        conversation_repository=conversation_repository,
        conversation_message_service=conversation_message_service,
        extracted_data_service=extracted_data_service,
        document_service=document_service,
    )


ConversationServiceDep = Annotated[ConversationService, Depends(get_conversation_service)]


def get_industry_data_service(
    industry_repository: IndustryRepositoryDep,
) -> IndustryDataService:
    return IndustryDataService(industry_repository=industry_repository)


IndustryDataServiceDep = Annotated[IndustryDataService, Depends(get_industry_data_service)]


def get_role_data_service(
    role_repository: RoleRepositoryDep,
) -> RoleDataService:
    return RoleDataService(role_repository=role_repository)


RoleDataServiceDep = Annotated[RoleDataService, Depends(get_role_data_service)]


def get_service_data_service(
    service_repository: ServiceRepositoryDep,
) -> ServiceDataService:
    return ServiceDataService(service_repository=service_repository)


ServiceDataServiceDep = Annotated[ServiceDataService, Depends(get_service_data_service)]

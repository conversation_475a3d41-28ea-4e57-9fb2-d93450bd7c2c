import logging
from typing import Any
from uuid import UUID

from constants.extracted_data import DataSourceType
from repositories import ExtractedDataRepository
from schemas import ExtractedData
from services.kx_dash.extractors import IndustryDataService, RoleDataService, ServiceDataService

from .parsers import KXDashDataParser


__all__ = ['ExtractedDataService']


logger = logging.getLogger(__name__)


class ExtractedDataService:
    _SOURCE_TYPE_TO_PARSER_MAP = {
        DataSourceType.KX_DASH: KXDashDataParser,
    }

    def __init__(
        self,
        extracted_data_repository: ExtractedDataRepository,
        industry_data_service: IndustryDataService,
        role_data_service: RoleDataService,
        service_data_service: ServiceDataService,
    ):
        self.extracted_data_repository = extracted_data_repository
        self.industry_data_service = industry_data_service
        self.role_data_service = role_data_service
        self.service_data_service = service_data_service

    async def delete_many(self, conversation_id: UUID) -> None:
        """
        Delete all extracted data for a conversation.

        Args:
            conversation_id: The ID of the conversation to delete extracted data from.

        Raises:
            EntityNotFoundError: If the conversation does not exist.
        """
        logger.debug('Deleting extracted data for conversation ID: %s', conversation_id)
        try:
            await self.extracted_data_repository.delete_many(conversation_id)
        except Exception as e:
            logger.error("Failed to delete extracted data for conversation ID '%s': %s", conversation_id, e)
            raise

    async def _process_activity_data(self, activity_data: dict[str, Any]) -> dict[str, Any]:
        """
        Process the activity data.
        """
        industries = await self.industry_data_service.list()
        services = await self.service_data_service.list()
        roles = await self.role_data_service.list()

        industry_name = activity_data.get('global_industry')
        if industry_name:
            industry = industries.get(industry_name)
            if industry:
                activity_data['industries'] = [industry['id']]

        service_name = activity_data.get('global_service')
        if service_name:
            service = services.get(service_name)
            if service:
                activity_data['services'] = [service['id']]

        lep_emails = activity_data.get('engagement_lep_emails')
        lcsp_emails = activity_data.get('engagement_lcsp_emails')
        manager_emails = activity_data.get('engagement_manager_emails')

        processed_roles = []
        if lep_emails:
            lep_role_id = roles.get('Lead Engagement Partner', {}).get('id')
            processed_roles.extend({'email': email, 'roles': [lep_role_id]} for email in lep_emails)
        if lcsp_emails:
            lcsp_role_id = roles.get('LCSP', {}).get('id')
            processed_roles.extend({'email': email, 'roles': [lcsp_role_id]} for email in lcsp_emails)
        if manager_emails:
            manager_role_id = roles.get('Engagement Manager', {}).get('id')
            processed_roles.extend({'email': email, 'roles': [manager_role_id]} for email in manager_emails)

        activity_data['roles'] = processed_roles

        return activity_data

    async def update(
        self, conversation_id: UUID, raw_data: dict[str, Any], source_type: DataSourceType
    ) -> ExtractedData:
        """
        Update KX Dash extracted data for a conversation.

        Args:
            conversation_id: The ID of the conversation to update extracted data for.
            activity_data: The data to update the extracted data with.

        Raises:
            EntityNotFoundError: If the conversation does not exist.
        """
        logger.debug('Started updating "%s" extracted data for conversation ID "%s"', source_type, conversation_id)
        try:
            extracted_data = await self.extracted_data_repository.get(
                conversation_id=conversation_id,
                data_source_type=source_type,
            ) or ExtractedData.create(conversation_id=conversation_id, data_source_type=source_type)
            processed_activity_data = await self._process_activity_data(raw_data)
            parser = self._SOURCE_TYPE_TO_PARSER_MAP[source_type]()
            await self.extracted_data_repository.update(parser(extracted_data, processed_activity_data))
            return extracted_data

        except Exception as e:
            logger.error(
                'Failed to update "%s" extracted data for conversation ID "%s": %s', source_type, conversation_id, e
            )
            raise

import logging
from typing import Any

from repositories.role import RoleRepository


__all__ = ['RoleDataService']

logger = logging.getLogger(__name__)


class RoleDataService:
    cached_roles: dict[str, dict[str, Any]] = {}

    def __init__(self, role_repository: RoleRepository):
        self.role_repository = role_repository

    @classmethod
    async def _set_cached_roles(cls, roles: list[dict[str, Any]]) -> None:
        cls.cached_roles = {role['title']: role for role in roles}

    async def list(self) -> dict[str, dict[str, Any]]:
        """
        List all roles.

        Returns:
            List of roles.

        Raises:
            Exception: If an error occurs while listing roles.
        """
        try:
            logger.info('Listing roles')
            if not self.cached_roles:
                roles = await self.role_repository.list()
                await self._set_cached_roles(roles)

            return self.cached_roles
        except Exception as e:
            logger.error('Error listing roles: %s', e)
            raise

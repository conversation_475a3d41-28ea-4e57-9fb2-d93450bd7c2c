import logging
from typing import Any

from repositories.industry import IndustryRepository


__all__ = ['IndustryDataService']

logger = logging.getLogger(__name__)


class IndustryDataService:
    cached_industries: dict[str, dict[str, Any]] = {}

    def __init__(self, industry_repository: IndustryRepository):
        self.industry_repository = industry_repository

    @classmethod
    async def _set_cached_industries(cls, industries: list[dict[str, Any]]) -> None:
        cls.cached_industries = {industry['name']: industry for industry in industries}

    async def list(self) -> dict[str, dict[str, Any]]:
        """
        List all industries.

        Returns:
            List of industries.

        Raises:
            Exception: If an error occurs while listing industries.
        """
        try:
            logger.info('Listing industries')
            if not self.cached_industries:
                industries = await self.industry_repository.list()
                await self._set_cached_industries(industries)

            return self.cached_industries
        except Exception as e:
            logger.error('Error listing industries: %s', e)
            raise
